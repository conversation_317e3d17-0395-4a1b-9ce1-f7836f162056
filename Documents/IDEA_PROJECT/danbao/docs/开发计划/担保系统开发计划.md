# 担保系统详细开发任务计划

## 项目概述

**项目名称**: 担保管理系统
**项目代码**: danbao
**开发模式**: 前后端分离
**技术架构**: Spring Boot 3.x + Vue 3.x
**模块位置**: nodal-module-danbao
**开发周期**: 预计 20-24 周

## 详细开发任务清单

### 阶段一：数据库设计与基础实体创建

#### 任务 1.1：创建担保申请相关数据表和实体
**功能描述**: 担保申请是整个担保业务的起点，客户通过系统提交担保申请，包含申请人信息、担保金额、担保期限、担保用途等核心信息。

**数据表设计** (严格按照系统规范):
- `danbao_application` (担保申请主表)
  - id (主键，BIGINT AUTO_INCREMENT)
  - create_date (创建日期，BIGINT，yyyyMMdd格式，自动填充)
  - application_no (申请编号，VARCHAR(32)，格式：DB + YYYYMMDD + 4位序号)
  - customer_id (客户ID，BIGINT)
  - customer_name (客户名称，VARCHAR(100))
  - customer_type (客户类型，TINYINT：1-企业，2-个人)
  - guarantee_amount (担保金额，DECIMAL(15,2))
  - guarantee_period (担保期限，INTEGER，单位：月)
  - guarantee_purpose (担保用途，VARCHAR(500))
  - application_date (申请日期，DATETIME)
  - status (申请状态，TINYINT：0-草稿，1-已提交，2-审批中，3-已通过，4-已拒绝，5-已撤回)
  - remark (备注，VARCHAR(500))
  - creator (创建者，VARCHAR(64))
  - create_time (创建时间，DATETIME，DEFAULT CURRENT_TIMESTAMP)
  - updater (更新者，VARCHAR(64))
  - update_time (更新时间，DATETIME，DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)
  - deleted (是否删除，BIT(1)，DEFAULT 0)
  - tenant_id (租户编号，BIGINT，DEFAULT 0，**自动租户隔离**)
  - INDEX idx_customer_id (customer_id)
  - INDEX idx_status (status)
  - INDEX idx_create_time (create_time)

**字典项设计**:
- 客户类型字典 (customer_type): 1-企业, 2-个人
- 申请状态字典 (application_status): 0-草稿, 1-已提交, 2-审批中, 3-已通过, 4-已拒绝, 5-已撤回
- 资料类型字典 (material_type): 1-营业执照, 2-财务报表, 3-征信报告, 4-其他

- `danbao_application_material` (申请资料表)
  - id (主键，BIGINT AUTO_INCREMENT)
  - create_date (创建日期，BIGINT，yyyyMMdd格式，自动填充)
  - application_id (申请ID，BIGINT)
  - material_type (资料类型，TINYINT：1-营业执照，2-财务报表，3-征信报告，4-其他)
  - material_name (资料名称，VARCHAR(200))
  - file_path (文件路径，VARCHAR(500))
  - file_size (文件大小，BIGINT，单位：字节)
  - file_type (文件类型，VARCHAR(50)，如：pdf、jpg、png等)
  - upload_time (上传时间，DATETIME)
  - creator (创建者，VARCHAR(64))
  - create_time (创建时间，DATETIME，DEFAULT CURRENT_TIMESTAMP)
  - updater (更新者，VARCHAR(64))
  - update_time (更新时间，DATETIME，DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)
  - deleted (是否删除，BIT(1)，DEFAULT 0)
  - tenant_id (租户编号，BIGINT，DEFAULT 0)
  - INDEX idx_application_id (application_id)
  - INDEX idx_material_type (material_type)

**开发任务** (严格按照"管理后台 - 担保申请"模块编码规范):
1. **数据库开发**
   - 创建 `danbao_application` 表SQL脚本（包含通用字段和create_date字段）
   - 创建 `danbao_application_material` 表SQL脚本
   - 为枚举类字段生成字典项SQL脚本
   - 生成完整SQL脚本到 `docs/sql/application_init.sql`
   - 连接数据库执行SQL脚本

2. **DO实体类开发** (参考AdminUserDO结构)
   - 创建 `ApplicationDO` 实体类
     - **租户隔离**：继承 `TenantBaseDO`（自动包含租户隔离功能）
     - 使用 `@TableName("danbao_application")`
     - 使用 `@KeySequence("danbao_application_seq")`
     - 使用标准注解：`@Data`、`@EqualsAndHashCode(callSuper = true)`、`@Builder`、`@NoArgsConstructor`、`@AllArgsConstructor`
     - 包含业务方法（如状态处理方法）
   - 创建 `ApplicationMaterialDO` 实体类（同样规范）

3. **Mapper接口开发** (参考AdminUserMapper结构)
   - 创建 `ApplicationMapper` 接口
     - 继承 `BaseMapperX<ApplicationDO>`
     - 实现 `selectPage` 方法，使用 `LambdaQueryWrapperX`
     - 添加自定义查询方法（如按状态查询）
   - 创建 `ApplicationMaterialMapper` 接口
   - 创建对应的XML文件（基础模板）

4. **VO类开发** (参考User VO结构)
   - 创建 `ApplicationPageReqVO` 继承 `PageParam`
     - 包含所有查询条件字段
     - 使用 `@Schema` 注解描述
     - 时间范围查询使用 `LocalDateTime[]`
   - 创建 `ApplicationSaveReqVO`
     - 包含id字段（用于新增和修改）
     - 使用 `@Schema` 注解和校验注解
   - 创建 `ApplicationRespVO`
     - 包含 `@ExcelProperty` 注解
     - 使用 `@ExcelIgnoreUnannotated`
   - 创建 `ApplicationMaterialRespVO` 等相关VO

5. **Service开发** (参考AdminUserService结构)
   - 创建 `ApplicationService` 接口
     - 方法命名：`createApplication`、`updateApplication`、`deleteApplication`
     - 方法签名：`getApplication(Long id)`、`getApplicationPage(ApplicationPageReqVO pageReqVO)`
     - 使用 `@Valid` 参数校验
   - 创建 `ApplicationServiceImpl` 实现类
     - 使用 `@Service` 和 `@Validated` 注解
     - 使用 `BeanUtils.toBean` 进行对象转换
     - 实现业务逻辑和数据校验

6. **Controller开发** (参考UserController结构)
   - 创建 `ApplicationController` 控制器
     - 使用 `@Tag(name = "管理后台 - 担保申请")`
     - 请求映射：`@RequestMapping("/danbao/application")`
     - 接口路径：`/create`、`/update`、`/delete`、`/get`、`/page`
     - 使用 `@PreAuthorize("@ss.hasPermission('danbao:application:权限')")`
     - 使用 `BeanUtils.toBean` 转换响应对象
     - 返回 `CommonResult<T>` 格式

7. **Convert转换器开发**
   - 创建 `ApplicationConvert` 接口
     - 使用 `@Mapper` 注解
     - 定义 `INSTANCE = Mappers.getMapper(ApplicationConvert.class)`
     - 实现各种VO和DO之间的转换方法

8. **菜单权限配置**
   - 创建"担保申请"主菜单 (type=2, parent_id=0)
   - 创建子菜单按钮 (type=3)：
     - `danbao:application:query` (查询)
     - `danbao:application:create` (新增)
     - `danbao:application:update` (编辑)
     - `danbao:application:delete` (删除)
     - `danbao:application:export` (导出)
   - 生成菜单权限SQL脚本

9. **单元测试开发**
   - 编写ApplicationMapper单元测试
   - 编写ApplicationService单元测试
   - 确保测试覆盖率100%

#### 任务 1.2：创建担保审批相关数据表和实体
**功能描述**: 担保审批是对担保申请进行多级审核的流程，包括初审、复审、终审等环节，每个环节都有相应的审批人员和审批意见。

**数据表设计**:
- `danbao_approval` (担保审批表)
  - id (主键)
  - application_id (申请ID)
  - approval_level (审批级别：1-初审，2-复审，3-终审)
  - approver_id (审批人ID)
  - approver_name (审批人姓名)
  - approval_result (审批结果：1-通过，2-拒绝，3-退回)
  - approval_opinion (审批意见)
  - approval_time (审批时间)
  - next_approver_id (下一审批人ID)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_approval_flow` (审批流程配置表)
  - id (主键)
  - flow_name (流程名称)
  - guarantee_amount_min (担保金额下限)
  - guarantee_amount_max (担保金额上限)
  - approval_levels (审批级别数量)
  - flow_config (流程配置JSON)
  - status (状态：0-禁用，1-启用)
  - creator/create_time/updater/update_time/deleted/tenant_id

**开发任务**:
1. 创建数据表SQL脚本
2. 创建DO实体类
3. 创建Mapper接口和XML
4. 创建VO类和Convert转换器
5. 集成Flowable工作流引擎
6. 编写审批流程配置功能
7. 编写单元测试

#### 任务 1.3：创建客户管理相关数据表和实体
**功能描述**: 客户管理是担保业务的基础，包括企业客户和个人客户的基本信息、资信评级、联系方式等信息维护。

**数据表设计**:
- `danbao_customer` (客户信息表)
  - id (主键)
  - customer_no (客户编号)
  - customer_name (客户名称)
  - customer_type (客户类型：1-企业，2-个人)
  - unified_social_credit_code (统一社会信用代码/身份证号)
  - legal_representative (法定代表人/客户姓名)
  - registered_capital (注册资本)
  - establishment_date (成立日期/出生日期)
  - business_scope (经营范围/职业)
  - contact_person (联系人)
  - contact_phone (联系电话)
  - contact_address (联系地址)
  - credit_rating (资信评级：AAA,AA,A,BBB,BB,B,CCC,CC,C,D)
  - risk_level (风险等级：1-低风险，2-中风险，3-高风险)
  - status (状态：0-禁用，1-启用)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_customer_finance` (客户财务信息表)
  - id (主键)
  - customer_id (客户ID)
  - report_year (报告年度)
  - total_assets (总资产)
  - total_liabilities (总负债)
  - net_assets (净资产)
  - operating_income (营业收入)
  - net_profit (净利润)
  - cash_flow (现金流量)
  - asset_liability_ratio (资产负债率)
  - creator/create_time/updater/update_time/deleted/tenant_id

**开发任务**:
1. 创建数据表SQL脚本
2. 创建DO实体类
3. 创建Mapper接口和XML
4. 创建VO类和Convert转换器
5. 实现客户信息CRUD功能
6. 实现客户资信评级功能
7. 编写单元测试

### 阶段二：核心业务功能开发

#### 任务 2.1：工作台功能开发
**功能描述**: 用户登录系统的主界面，提供个性化的工作台内容，包括通知消息、待办事项、已办事项、预警信息、业绩统计等功能。

**数据表设计**:
- `danbao_notice` (通知消息表)
  - id, title (标题), content (内容), notice_type (通知类型), target_type (目标类型)
  - target_users (目标用户), publish_time (发布时间), status (状态)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_todo_task` (待办任务表)
  - id, task_type (任务类型), task_title (任务标题), task_content (任务内容)
  - business_id (业务ID), assignee_id (处理人ID), priority (优先级)
  - due_date (截止日期), status (状态), create_time

- `danbao_performance_stats` (业绩统计表)
  - id, user_id (用户ID), stat_date (统计日期), customer_count (客户数量)
  - business_count (业务数量), guarantee_amount (担保金额), ranking (排名)
  - creator/create_time/updater/update_time/deleted/tenant_id

**开发任务**:
1. **数据库开发**
   - 创建工作台相关数据表SQL脚本
   - 生成字典项SQL脚本（通知类型、任务类型、优先级等）
   - 生成完整SQL脚本到 `docs/sql/dashboard_init.sql`
2. **后端开发**
   - 创建工作台相关DO实体类、Mapper、VO类、Convert转换器
   - 创建 `DashboardService` 接口和实现类
   - 创建 `DashboardController` 控制器
3. **前端开发**
   - 创建工作台主页面 `src/views/dashboard/index.vue`
   - 创建通知消息组件、待办事项组件、业绩统计组件
4. **菜单权限配置**
   - 创建"工作台"主菜单和相关权限

#### 任务 2.2：担保申请管理功能开发
**功能描述**: 实现担保申请的完整生命周期管理，包括申请录入、资料上传、状态跟踪、信息修改等功能。

**后端开发任务**:
1. **ApplicationService接口和实现类**
   ```java
   // 主要方法：
   - PageResult<ApplicationPageRespVO> getApplicationPage(ApplicationPageReqVO pageReqVO)
   - ApplicationRespVO getApplication(Long id)
   - Long createApplication(ApplicationSaveReqVO createReqVO)
   - void updateApplication(ApplicationSaveReqVO updateReqVO)
   - void deleteApplication(Long id)
   - void submitApplication(Long id) // 提交申请
   - void withdrawApplication(Long id) // 撤回申请
   - void uploadMaterial(Long applicationId, MultipartFile file, Integer materialType)
   - List<ApplicationMaterialRespVO> getMaterialList(Long applicationId)
   ```

2. **ApplicationController控制器**
   ```java
   // 主要接口：
   - GET /admin-api/danbao/application/page // 分页查询
   - GET /admin-api/danbao/application/get/{id} // 详情查询
   - POST /admin-api/danbao/application/create // 新增申请
   - PUT /admin-api/danbao/application/update // 修改申请
   - DELETE /admin-api/danbao/application/delete/{id} // 删除申请
   - PUT /admin-api/danbao/application/submit/{id} // 提交申请
   - PUT /admin-api/danbao/application/withdraw/{id} // 撤回申请
   - POST /admin-api/danbao/application/upload-material // 上传资料
   - GET /admin-api/danbao/application/material-list/{applicationId} // 资料列表
   ```

3. **数据校验规则**
   - 申请编号自动生成且唯一
   - 担保金额必须大于0，最大不超过系统配置值
   - 担保期限必须在1-120个月之间
   - 客户信息必须真实有效
   - 上传文件大小不超过10MB，支持pdf、doc、docx、jpg、png格式

**前端开发任务**:
1. **申请列表页面** (`src/views/danbao/application/index.vue`)
   - 搜索条件：申请编号、客户名称、申请状态、申请日期范围
   - 列表字段：申请编号、客户名称、担保金额、担保期限、申请状态、申请日期、操作
   - 操作按钮：查看、编辑、删除、提交、撤回

2. **申请表单页面** (`src/views/danbao/application/form.vue`)
   - 基本信息：客户选择、担保金额、担保期限、担保用途
   - 资料上传：支持多文件上传，文件预览
   - 表单校验：必填项校验、格式校验、业务规则校验

3. **申请详情页面** (`src/views/danbao/application/detail.vue`)
   - 申请信息展示
   - 资料列表展示
   - 审批记录展示
   - 操作日志展示

#### 任务 2.2：担保审批管理功能开发
**功能描述**: 实现担保申请的多级审批流程，包括审批任务分配、审批意见录入、审批结果处理等功能。

**后端开发任务**:
1. **ApprovalService接口和实现类**
   ```java
   // 主要方法：
   - PageResult<ApprovalTaskPageRespVO> getApprovalTaskPage(ApprovalTaskPageReqVO pageReqVO)
   - ApprovalTaskRespVO getApprovalTask(Long id)
   - void approveApplication(ApprovalReqVO approvalReqVO) // 审批申请
   - void rejectApplication(ApprovalReqVO approvalReqVO) // 拒绝申请
   - void returnApplication(ApprovalReqVO approvalReqVO) // 退回申请
   - List<ApprovalHistoryRespVO> getApprovalHistory(Long applicationId) // 审批历史
   - void configApprovalFlow(ApprovalFlowConfigReqVO configReqVO) // 配置审批流程
   ```

2. **ApprovalController控制器**
   ```java
   // 主要接口：
   - GET /admin-api/danbao/approval/task-page // 待审批任务分页
   - GET /admin-api/danbao/approval/task/{id} // 审批任务详情
   - POST /admin-api/danbao/approval/approve // 审批通过
   - POST /admin-api/danbao/approval/reject // 审批拒绝
   - POST /admin-api/danbao/approval/return // 审批退回
   - GET /admin-api/danbao/approval/history/{applicationId} // 审批历史
   - POST /admin-api/danbao/approval/config-flow // 配置审批流程
   ```

3. **工作流集成**
   - 集成Flowable工作流引擎
   - 定义审批流程BPMN文件
   - 实现流程启动、任务分配、流程跳转
   - 实现审批超时提醒功能

**前端开发任务**:
1. **待审批任务列表** (`src/views/danbao/approval/task.vue`)
2. **审批处理页面** (`src/views/danbao/approval/handle.vue`)
3. **审批历史页面** (`src/views/danbao/approval/history.vue`)
4. **审批流程配置** (`src/views/danbao/approval/flow-config.vue`)

#### 任务 2.3：反担保物管理功能开发
**功能描述**: 管理担保业务中的反担保物，包括押品类别管理、押品信息管理、押品入库出库、押品查询等功能。

**数据表设计**:
- `danbao_collateral_category` (押品类别表)
  - id, category_code (类别编码), category_name (类别名称), parent_id (父级ID)
  - level (级别), sort (排序), status (状态)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_collateral` (押品信息表)
  - id, customer_id (客户ID), category_id (类别ID), collateral_name (押品名称)
  - collateral_type (押品类型), owner_name (所有权人), owner_cert_type (证件类型)
  - owner_cert_no (证件号码), mortgage_rate (抵质押率), original_value (原始价值)
  - current_value (当前价值), status (状态), description (说明)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_collateral_detail` (押品详情表)
  - id, collateral_id (押品ID), detail_type (详情类型), detail_data (详情数据JSON)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_collateral_storage` (押品入出库表)
  - id, collateral_id (押品ID), operation_type (操作类型：1-入库，2-出库)
  - storage_date (入库日期), storage_address (存放地址), storage_org (保管机构)
  - keeper (保管人), operator (经办人), reason (原因), status (状态)
  - creator/create_time/updater/update_time/deleted/tenant_id

**开发任务**:
1. **数据库开发**
   - 创建反担保物相关数据表SQL脚本
   - 生成押品类别基础数据SQL脚本
   - 生成完整SQL脚本到 `docs/sql/collateral_init.sql`
2. **后端开发**
   - 创建反担保物相关DO实体类、Mapper、VO类、Convert转换器
   - 创建 `CollateralService` 接口和实现类
   - 创建 `CollateralController` 控制器
3. **前端开发**
   - 创建押品管理页面 `src/views/danbao/collateral/index.vue`
   - 创建押品详情页面、入库出库页面
4. **菜单权限配置**
   - 创建"反担保物管理"主菜单和相关权限

#### 任务 2.4：客户管理功能开发
**功能描述**: 实现客户信息的全生命周期管理，包括客户档案建立、信息维护、资信评级、风险评估等功能。

**后端开发任务**:
1. **CustomerService接口和实现类**
   ```java
   // 主要方法：
   - PageResult<CustomerPageRespVO> getCustomerPage(CustomerPageReqVO pageReqVO)
   - CustomerRespVO getCustomer(Long id)
   - Long createCustomer(CustomerSaveReqVO createReqVO)
   - void updateCustomer(CustomerSaveReqVO updateReqVO)
   - void deleteCustomer(Long id)
   - void updateCreditRating(Long customerId, String creditRating) // 更新资信评级
   - void updateRiskLevel(Long customerId, Integer riskLevel) // 更新风险等级
   - CustomerFinanceRespVO getCustomerFinance(Long customerId) // 获取财务信息
   - void saveCustomerFinance(CustomerFinanceSaveReqVO saveReqVO) // 保存财务信息
   - List<CustomerRespVO> getCustomerOptions() // 获取客户选项（用于下拉框）
   ```

2. **CustomerController控制器**
   ```java
   // 主要接口：
   - GET /admin-api/danbao/customer/page // 分页查询
   - GET /admin-api/danbao/customer/get/{id} // 详情查询
   - POST /admin-api/danbao/customer/create // 新增客户
   - PUT /admin-api/danbao/customer/update // 修改客户
   - DELETE /admin-api/danbao/customer/delete/{id} // 删除客户
   - PUT /admin-api/danbao/customer/credit-rating // 更新资信评级
   - PUT /admin-api/danbao/customer/risk-level // 更新风险等级
   - GET /admin-api/danbao/customer/finance/{customerId} // 财务信息
   - POST /admin-api/danbao/customer/finance // 保存财务信息
   - GET /admin-api/danbao/customer/options // 客户选项
   ```

**前端开发任务**:
1. **客户列表页面** (`src/views/danbao/customer/index.vue`)
2. **客户表单页面** (`src/views/danbao/customer/form.vue`)
3. **客户详情页面** (`src/views/danbao/customer/detail.vue`)
4. **财务信息管理** (`src/views/danbao/customer/finance.vue`)

#### 任务 2.5：档案管理功能开发
**功能描述**: 管理担保业务过程中的所有档案资料，包括档案归档、档案变更、借阅申请、出借审批、档案归还等功能。

**数据表设计**:
- `danbao_archive` (档案表)
  - id, archive_no (档案编号), archive_name (档案名称), archive_type (档案类型)
  - business_id (业务ID), customer_id (客户ID), file_count (文件数量)
  - archive_status (归档状态), storage_location (存放位置), keeper (保管人)
  - archive_date (归档日期), description (说明)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_archive_file` (档案文件表)
  - id, archive_id (档案ID), file_name (文件名称), file_type (文件类型)
  - file_path (文件路径), file_size (文件大小), page_count (页数)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_archive_borrow` (档案借阅表)
  - id, archive_id (档案ID), borrower_id (借阅人ID), borrower_name (借阅人姓名)
  - borrow_reason (借阅原因), borrow_date (借阅日期), expected_return_date (预计归还日期)
  - actual_return_date (实际归还日期), borrow_status (借阅状态), approver_id (审批人ID)
  - approval_opinion (审批意见), approval_time (审批时间)
  - creator/create_time/updater/update_time/deleted/tenant_id

**开发任务**:
1. **数据库开发**
   - 创建档案管理相关数据表SQL脚本
   - 生成字典项SQL脚本（档案类型、借阅状态等）
   - 生成完整SQL脚本到 `docs/sql/archive_init.sql`
2. **后端开发**
   - 创建档案管理相关DO实体类、Mapper、VO类、Convert转换器
   - 创建 `ArchiveService` 接口和实现类
   - 创建 `ArchiveController` 控制器
3. **前端开发**
   - 创建档案管理页面 `src/views/danbao/archive/index.vue`
   - 创建档案借阅页面、归还页面
4. **菜单权限配置**
   - 创建"档案管理"主菜单和相关权限

#### 任务 2.6：综合管理功能开发
**功能描述**: 管理担保业务中的合作机构，包括资金机构管理、合作机构管理等功能。

**数据表设计**:
- `danbao_partner_institution` (合作机构表)
  - id, institution_name (机构名称), institution_type (机构类型), credit_code (统一社会信用代码)
  - institution_category (机构分类), contact_person (联系人), contact_phone (联系电话)
  - credit_amount (授信金额), credit_expire_date (授信到期日), cooperation_status (合作状态)
  - address (地址), description (机构简介)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_institution_account` (机构账户表)
  - id, institution_id (机构ID), account_name (账户名称), account_no (账户号码)
  - bank_name (开户行), account_type (账户类型), status (状态)
  - creator/create_time/updater/update_time/deleted/tenant_id

**开发任务**:
1. **数据库开发**
   - 创建综合管理相关数据表SQL脚本
   - 生成字典项SQL脚本（机构类型、机构分类等）
   - 生成完整SQL脚本到 `docs/sql/institution_init.sql`
2. **后端开发**
   - 创建综合管理相关DO实体类、Mapper、VO类、Convert转换器
   - 创建 `InstitutionService` 接口和实现类
   - 创建 `InstitutionController` 控制器
3. **前端开发**
   - 创建合作机构管理页面 `src/views/danbao/institution/index.vue`
4. **菜单权限配置**
   - 创建"综合管理"主菜单和相关权限

### 阶段三：风险管控功能开发

#### 任务 3.1：风险评估功能开发
**功能描述**: 建立风险评估模型，对客户和担保项目进行风险评估，生成风险评估报告。

**数据表设计**:
- `danbao_risk_assessment` (风险评估表)
  - id, customer_id, application_id, assessment_type (评估类型)
  - financial_score (财务评分), credit_score (信用评分)
  - industry_score (行业评分), regional_score (区域评分)
  - total_score (总评分), risk_level (风险等级)
  - assessment_result (评估结果), assessment_report (评估报告)
  - assessor_id, assessment_time

- `danbao_risk_rule` (风险规则表)
  - id, rule_name, rule_type, rule_condition (规则条件JSON)
  - rule_score (规则评分), weight (权重), status

**开发任务**:
1. 创建风险评估规则引擎
2. 实现客户风险评估算法
3. 实现项目风险评估算法
4. 生成风险评估报告
5. 风险评估结果可视化展示

#### 任务 3.2：风险监控功能开发
**功能描述**: 实时监控担保项目风险状况，设置预警规则，及时发现和处理风险。

**数据表设计**:
- `danbao_risk_monitor` (风险监控表)
- `danbao_risk_warning` (风险预警表)
- `danbao_risk_event` (风险事件表)

**开发任务**:
1. 实现风险监控指标配置
2. 实现风险预警规则设置
3. 实现风险事件记录和处理
4. 风险监控大屏展示
5. 风险预警消息推送

### 阶段四：财务管理功能开发

#### 任务 4.1：担保费用管理功能开发
**功能描述**: 管理担保业务相关的各项费用，包括担保费、评估费、律师费等的计算、收取和结算。

**数据表设计**:
- `danbao_fee_config` (费用配置表)
  - id, fee_type (费用类型), fee_name (费用名称)
  - calculation_method (计算方式：1-固定金额，2-按比例)
  - fee_rate (费率), min_amount (最小金额), max_amount (最大金额)
  - status, creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_fee_record` (费用记录表)
  - id, application_id, customer_id, fee_type, fee_amount
  - payment_status (缴费状态), payment_time, payment_method
  - invoice_status (开票状态), invoice_no (发票号码)
  - creator/create_time/updater/update_time/deleted/tenant_id

**后端开发任务**:
1. **FeeService接口和实现类**
   ```java
   // 主要方法：
   - BigDecimal calculateFee(Long applicationId, Integer feeType) // 计算费用
   - void recordFeePayment(FeePaymentReqVO paymentReqVO) // 记录缴费
   - PageResult<FeeRecordPageRespVO> getFeeRecordPage(FeeRecordPageReqVO pageReqVO)
   - void generateInvoice(Long feeRecordId) // 生成发票
   - List<FeeStatisticsRespVO> getFeeStatistics(FeeStatisticsReqVO reqVO) // 费用统计
   ```

2. **FeeController控制器**
   ```java
   // 主要接口：
   - POST /admin-api/danbao/fee/calculate // 费用计算
   - POST /admin-api/danbao/fee/payment // 记录缴费
   - GET /admin-api/danbao/fee/record-page // 费用记录分页
   - POST /admin-api/danbao/fee/invoice // 生成发票
   - GET /admin-api/danbao/fee/statistics // 费用统计
   ```

**前端开发任务**:
1. **费用配置管理** (`src/views/danbao/fee/config.vue`)
2. **费用记录管理** (`src/views/danbao/fee/record.vue`)
3. **费用统计报表** (`src/views/danbao/fee/statistics.vue`)

#### 任务 4.2：代偿管理功能开发
**功能描述**: 管理担保代偿业务，包括代偿申请、代偿支付、追偿管理、损失核销等功能。

**数据表设计**:
- `danbao_compensation` (代偿记录表)
  - id, application_id, customer_id, guarantee_amount (担保金额)
  - compensation_amount (代偿金额), compensation_date (代偿日期)
  - compensation_reason (代偿原因), compensation_status (代偿状态)
  - recovery_amount (追偿金额), recovery_status (追偿状态)
  - loss_amount (损失金额), write_off_amount (核销金额)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_recovery_record` (追偿记录表)
  - id, compensation_id, recovery_amount, recovery_date
  - recovery_method (追偿方式), recovery_status, remark
  - creator/create_time/updater/update_time/deleted/tenant_id

**开发任务**:
1. 代偿申请和审批流程
2. 代偿资金支付管理
3. 追偿计划制定和执行
4. 损失核销管理
5. 代偿统计分析

### 阶段五：移动端和系统设置功能开发

#### 任务 5.1：移动端管理功能开发
**功能描述**: 面向机构内部员工的移动端应用，集成至钉钉、企微等办公平台，实现移动化办公。

**数据表设计**:
- `danbao_mobile_user` (移动端用户表)
  - id, user_id (用户ID), platform_type (平台类型：1-钉钉，2-企微), platform_user_id (平台用户ID)
  - device_id (设备ID), last_login_time (最后登录时间), status (状态)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_mobile_message` (移动端消息表)
  - id, message_type (消息类型), title (标题), content (内容)
  - target_user_id (目标用户ID), business_id (业务ID), send_time (发送时间)
  - read_time (阅读时间), status (状态)
  - creator/create_time/updater/update_time/deleted/tenant_id

**开发任务**:
1. **数据库开发**
   - 创建移动端相关数据表SQL脚本
   - 生成字典项SQL脚本（平台类型、消息类型等）
   - 生成完整SQL脚本到 `docs/sql/mobile_init.sql`
2. **后端开发**
   - 创建移动端相关DO实体类、Mapper、VO类、Convert转换器
   - 创建 `MobileService` 接口和实现类
   - 创建 `MobileController` 控制器（提供移动端API）
3. **移动端开发**
   - 创建移动端页面组件（基于H5）
   - 集成钉钉、企微SDK
   - 实现客户管理、业务查询、线上审批等功能
4. **第三方集成**
   - 集成钉钉开放平台API
   - 集成企微开放平台API
   - 实现消息推送功能

#### 任务 5.2：系统设置功能开发
**功能描述**: 系统的基础配置管理，包括机构设置、客户设置、产品设置、保后设置、基础设置等功能。

**数据表设计**:
- `danbao_product` (担保产品表)
  - id, product_code (产品编码), product_name (产品名称), product_type (产品类型)
  - product_category (产品分类), min_amount (最小金额), max_amount (最大金额)
  - min_period (最小期限), max_period (最大期限), fee_rate (费率)
  - risk_level (风险等级), status (状态), description (产品描述)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_product_config` (产品配置表)
  - id, product_id (产品ID), config_type (配置类型), config_key (配置键)
  - config_value (配置值), description (说明)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_workflow_config` (工作流配置表)
  - id, workflow_name (流程名称), workflow_type (流程类型), workflow_definition (流程定义)
  - status (状态), version (版本)
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_template` (模板表)
  - id, template_name (模板名称), template_type (模板类型), template_content (模板内容)
  - template_variables (模板变量), status (状态)
  - creator/create_time/updater/update_time/deleted/tenant_id

**开发任务**:
1. **数据库开发**
   - 创建系统设置相关数据表SQL脚本
   - 生成字典项SQL脚本（产品类型、配置类型等）
   - 生成完整SQL脚本到 `docs/sql/system_config_init.sql`
2. **后端开发**
   - 创建系统设置相关DO实体类、Mapper、VO类、Convert转换器
   - 创建 `ProductService`、`WorkflowConfigService`、`TemplateService` 等接口和实现类
   - 创建相应的Controller控制器
3. **前端开发**
   - 创建产品管理页面 `src/views/danbao/product/index.vue`
   - 创建工作流配置页面 `src/views/danbao/workflow/index.vue`
   - 创建模板管理页面 `src/views/danbao/template/index.vue`
4. **菜单权限配置**
   - 创建"系统设置"主菜单和相关权限

### 阶段六：统计分析功能开发

#### 任务 5.1：业务统计功能开发
**功能描述**: 提供全面的业务统计分析功能，包括担保业务量、客户分布、产品使用情况等多维度统计。

**后端开发任务**:
1. **StatisticsService接口和实现类**
   ```java
   // 主要方法：
   - BusinessStatisticsRespVO getBusinessStatistics(StatisticsReqVO reqVO) // 业务统计
   - List<CustomerDistributionRespVO> getCustomerDistribution() // 客户分布
   - List<ProductUsageRespVO> getProductUsage(StatisticsReqVO reqVO) // 产品使用统计
   - List<RegionalBusinessRespVO> getRegionalBusiness() // 区域业务统计
   - DashboardDataRespVO getDashboardData() // 仪表盘数据
   ```

2. **StatisticsController控制器**
   ```java
   // 主要接口：
   - GET /admin-api/danbao/statistics/business // 业务统计
   - GET /admin-api/danbao/statistics/customer-distribution // 客户分布
   - GET /admin-api/danbao/statistics/product-usage // 产品使用统计
   - GET /admin-api/danbao/statistics/regional-business // 区域业务统计
   - GET /admin-api/danbao/statistics/dashboard // 仪表盘数据
   ```

**前端开发任务**:
1. **业务统计大屏** (`src/views/danbao/statistics/dashboard.vue`)
2. **业务分析报表** (`src/views/danbao/statistics/business.vue`)
3. **客户分析报表** (`src/views/danbao/statistics/customer.vue`)

#### 任务 5.2：报表管理功能开发
**功能描述**: 提供灵活的报表生成和导出功能，支持多种格式的报表输出。

**数据表设计**:
- `danbao_report_template` (报表模板表)
  - id, template_name, template_type, template_config (模板配置JSON)
  - sql_content (SQL内容), status
  - creator/create_time/updater/update_time/deleted/tenant_id

- `danbao_report_task` (报表任务表)
  - id, template_id, task_name, parameters (参数JSON)
  - execute_status (执行状态), file_path (文件路径)
  - creator/create_time/updater/update_time/deleted/tenant_id

**开发任务**:
1. 报表模板管理
2. 报表生成引擎
3. 报表导出功能（Excel、PDF）
4. 定时报表任务
5. 报表权限控制

## 具体开发任务执行清单

### 第一阶段：数据库设计与基础实体 (第1-2周)

#### 任务清单
- [ ] **任务1.1：担保申请数据表和实体创建**
  - [x] **统一数据库开发** (一次性生成和执行)
    - [x] **主动创建目录**：确保 `docs/sql/` 目录存在
    - [x] **表结构设计**：创建 `danbao_application` 和 `danbao_application_material` 表
    - [x] **字典项数据**：生成客户类型、申请状态、资料类型字典项
    - [x] **菜单权限配置**：生成主菜单和子菜单按钮（包含完整路由信息）
    - [x] **模拟数据生成**：生成测试数据（租户ID=1）
    - [x] **统一SQL脚本**：生成到 `docs/sql/application_init.sql`
    - [ ] **自动执行**：连接数据库执行脚本（需要通过IDE执行）
  - [x] **后端开发** (严格参考AdminUserDO结构)
    - [x] **主动创建目录**：确保完整的后端包结构存在
      - [x] `com.nodal.module.danbao.dal.dataobject.application`
      - [x] `com.nodal.module.danbao.dal.mysql.application`
      - [x] `com.nodal.module.danbao.controller.admin.application.vo`
      - [x] `com.nodal.module.danbao.service.application`
      - [x] `com.nodal.module.danbao.convert.application`
    - [x] **DO实体类开发**：
      - [x] 创建 `ApplicationDO` 实体类
        - [x] **租户隔离**：继承 `TenantBaseDO`，使用 `@TableName("danbao_application")`
        - [x] 添加 `@KeySequence("danbao_application_seq")`
        - [x] 包含所有业务字段和注释
        - [x] 添加业务方法（如申请编号生成、状态处理等）
      - [x] 创建 `ApplicationMaterialDO` 实体类（同样规范）
  - [x] **Mapper接口开发** (严格参考AdminUserMapper结构)
    - [x] 创建 `ApplicationMapper` 接口
      - [x] 继承 `BaseMapperX<ApplicationDO>`
      - [x] 实现 `selectPage` 方法，使用 `LambdaQueryWrapperX`
      - [x] 添加按状态查询、按客户查询等自定义方法
    - [x] 创建 `ApplicationMaterialMapper` 接口
    - [ ] 创建对应的XML文件（基础模板）
  - [x] **VO类开发** (严格参考User VO结构)
    - [x] 创建 `ApplicationPageReqVO` 继承 `PageParam`
      - [x] 包含申请编号、客户名称、状态、申请日期范围等查询条件
      - [x] 使用 `@Schema` 注解描述每个字段
    - [x] 创建 `ApplicationSaveReqVO`
      - [x] 包含id字段（用于新增和修改判断）
      - [x] 使用 `@Schema` 注解和 `@NotNull`、`@NotBlank` 等校验注解
    - [x] 创建 `ApplicationRespVO`
      - [x] 包含 `@ExcelProperty` 注解支持导出
      - [x] 使用 `@ExcelIgnoreUnannotated` 注解
    - [x] 创建 `ApplicationMaterialSaveReqVO`、`ApplicationMaterialRespVO` 等
  - [x] **Convert转换器开发**
    - [x] 创建 `ApplicationConvert` 接口
      - [x] 使用 `@Mapper` 注解
      - [x] 定义 `INSTANCE = Mappers.getMapper(ApplicationConvert.class)`
      - [x] 实现VO和DO之间的转换方法
  - [x] **Service开发** (严格参考UserService结构)
    - [x] 创建 `ApplicationService` 接口
      - [x] 方法命名：`createApplication`、`updateApplication`、`deleteApplication`
      - [x] 方法签名：`getApplication(Long id)`、`getApplicationPage(ApplicationPageReqVO pageReqVO)`
      - [x] 使用 `@Valid` 参数校验
    - [x] 创建 `ApplicationServiceImpl` 实现类
      - [x] 使用 `@Service` 和 `@Validated` 注解
      - [x] 使用 `BeanUtils.toBean` 进行对象转换
      - [x] 实现申请编号自动生成逻辑
      - [x] 实现状态变更业务逻辑
      - [x] 实现数据校验（如客户存在性、金额范围等）
  - [x] **Controller开发** (严格参考UserController结构)
    - [x] 创建 `ApplicationController` 控制器
      - [x] 使用 `@Tag(name = "管理后台 - 担保申请")`
      - [x] 请求映射：`@RequestMapping("/danbao/application")`
      - [x] 实现标准CRUD接口：`/create`、`/update`、`/delete`、`/get`、`/page`
      - [x] 使用 `@PreAuthorize("@ss.hasPermission('danbao:application:权限')")`
      - [x] 使用 `BeanUtils.toBean` 转换响应对象
      - [x] 实现申请资料管理接口
      - [x] 实现申请提交接口 `/submit/{id}`
      - [x] 实现申请撤回接口 `/withdraw/{id}`
  - [ ] **前端开发** (参考"管理后台 - 担保申请"模块结构)
    - [ ] **主动创建目录**：确保完整的前端目录结构存在
      - [ ] `danbao-admin/src/api/danbao/application`
      - [ ] `danbao-admin/src/views/danbao/application`
    - [ ] **API接口开发**：
      - [ ] 创建API接口文件 `src/api/danbao/application/index.ts`
      - [ ] 创建数据类型定义（ApplicationVO、ApplicationMaterialVO等）
    - [ ] **页面组件开发**：
      - [ ] 创建申请列表页面 `src/views/danbao/application/index.vue`
      - [ ] 创建申请表单页面 `src/views/danbao/application/ApplicationForm.vue`
      - [ ] 创建申请详情页面 `src/views/danbao/application/ApplicationDetail.vue`
      - [ ] 创建资料上传组件 `src/views/danbao/application/ApplicationMaterialForm.vue`
    - [ ] **字典类型配置**：添加到 `src/utils/dict.ts`
  - [x] **菜单权限配置** (不创建独立路由文件)
    - [x] **重要原则**：路由信息完全维护在系统菜单管理表中
    - [x] 创建"担保申请"主菜单 (type=2, parent_id=0, 包含完整路由信息)
    - [x] 创建子菜单按钮 (type=3, 查询、新增、编辑、删除、导出权限)
    - [x] 生成菜单权限SQL脚本（包含在统一SQL脚本中）
  - [x] **单元测试**
    - [x] 编写ApplicationMapper单元测试
    - [x] 编写ApplicationService单元测试
    - [x] 确保测试覆盖率100%

- [/] **任务1.2：担保审批数据表和实体创建**
  - [x] 创建 `danbao_approval` 表SQL脚本
  - [x] 创建 `danbao_approval_flow` 表SQL脚本
  - [x] 创建审批相关DO实体类（ApprovalDO、ApprovalFlowDO）
  - [x] 创建审批相关Mapper接口（ApprovalMapper、ApprovalFlowMapper）
  - [x] 创建审批相关VO类（ApprovalPageReqVO、ApprovalHandleReqVO、ApprovalRespVO）
  - [ ] 创建审批相关Convert转换器
  - [ ] 创建审批相关Service接口和实现
  - [ ] 创建审批相关Controller
  - [ ] 编写审批相关单元测试

- [ ] **任务1.3：客户管理数据表和实体创建**
  - [ ] 创建 `danbao_customer` 表SQL脚本
  - [ ] 创建 `danbao_customer_finance` 表SQL脚本
  - [ ] 创建客户相关DO实体类
  - [ ] 创建客户相关Mapper接口和XML
  - [ ] 创建客户相关VO类和Convert转换器
  - [ ] 实现客户编号生成规则
  - [ ] 编写客户相关单元测试

#### 验收标准
- 所有数据表创建成功，字段类型和约束正确
- 所有实体类字段映射正确，包含通用字段
- 所有Mapper接口方法完整，XML配置正确
- 所有VO类字段完整，校验注解正确
- 单元测试覆盖率达到80%以上

### 第二阶段：核心业务功能开发 (第3-8周)

#### 任务清单
- [ ] **任务2.1：工作台功能开发**
  - [ ] **数据库开发**
    - [ ] 创建 `danbao_notice` 表SQL脚本
    - [ ] 创建 `danbao_todo_task` 表SQL脚本
    - [ ] 创建 `danbao_performance_stats` 表SQL脚本
    - [ ] 生成字典项SQL脚本（通知类型、任务类型、优先级等）
    - [ ] 生成完整SQL脚本到 `docs/sql/dashboard_init.sql`
    - [ ] 连接数据库执行SQL脚本
  - [ ] **后端开发** (参考"管理后台 - 担保申请"模块结构)
    - [ ] 创建工作台相关DO实体类
    - [ ] 创建工作台相关Mapper接口和XML
    - [ ] 创建工作台相关VO类和Convert转换器
    - [ ] 创建 `DashboardService` 接口和实现类
    - [ ] 创建 `DashboardController` 控制器
  - [ ] **前端开发** (参考"管理后台 - 担保申请"模块结构)
    - [ ] 创建工作台主页面 `src/views/dashboard/index.vue`
    - [ ] 创建通知消息组件
    - [ ] 创建待办事项组件
    - [ ] 创建业绩统计组件
  - [ ] **菜单权限配置**
    - [ ] 创建"工作台"主菜单
    - [ ] 创建相关权限按钮
  - [ ] **单元测试**
    - [ ] 编写工作台相关单元测试

- [ ] **任务2.2：担保申请管理功能开发**
  - [ ] 创建 `ApplicationService` 接口
  - [ ] 实现 `ApplicationServiceImpl` 业务逻辑
    - [ ] 实现分页查询方法 `getApplicationPage`
    - [ ] 实现详情查询方法 `getApplication`
    - [ ] 实现新增申请方法 `createApplication`
    - [ ] 实现修改申请方法 `updateApplication`
    - [ ] 实现删除申请方法 `deleteApplication`
    - [ ] 实现提交申请方法 `submitApplication`
    - [ ] 实现撤回申请方法 `withdrawApplication`
    - [ ] 实现资料上传方法 `uploadMaterial`
    - [ ] 实现资料列表方法 `getMaterialList`
  - [ ] 创建 `ApplicationController` 控制器
    - [ ] 实现分页查询接口 `GET /admin-api/danbao/application/page`
    - [ ] 实现详情查询接口 `GET /admin-api/danbao/application/get/{id}`
    - [ ] 实现新增申请接口 `POST /admin-api/danbao/application/create`
    - [ ] 实现修改申请接口 `PUT /admin-api/danbao/application/update`
    - [ ] 实现删除申请接口 `DELETE /admin-api/danbao/application/delete/{id}`
    - [ ] 实现提交申请接口 `PUT /admin-api/danbao/application/submit/{id}`
    - [ ] 实现撤回申请接口 `PUT /admin-api/danbao/application/withdraw/{id}`
    - [ ] 实现资料上传接口 `POST /admin-api/danbao/application/upload-material`
    - [ ] 实现资料列表接口 `GET /admin-api/danbao/application/material-list/{applicationId}`
  - [ ] 实现申请编号自动生成逻辑
  - [ ] 实现文件上传和存储功能
  - [ ] 添加数据校验和业务规则校验
  - [ ] 编写Service和Controller单元测试
  - [ ] 前端申请列表页面开发 `src/views/danbao/application/index.vue`
  - [ ] 前端申请表单页面开发 `src/views/danbao/application/form.vue`
  - [ ] 前端申请详情页面开发 `src/views/danbao/application/detail.vue`
  - [ ] 前端API接口封装 `src/api/danbao/application.ts`

- [ ] **任务2.2：担保审批管理功能开发**
  - [ ] 创建 `ApprovalService` 接口
  - [ ] 实现 `ApprovalServiceImpl` 业务逻辑
    - [ ] 实现待审批任务分页查询 `getApprovalTaskPage`
    - [ ] 实现审批任务详情查询 `getApprovalTask`
    - [ ] 实现审批通过方法 `approveApplication`
    - [ ] 实现审批拒绝方法 `rejectApplication`
    - [ ] 实现审批退回方法 `returnApplication`
    - [ ] 实现审批历史查询 `getApprovalHistory`
    - [ ] 实现审批流程配置 `configApprovalFlow`
  - [ ] 创建 `ApprovalController` 控制器
  - [ ] 集成Flowable工作流引擎
    - [ ] 配置Flowable数据源和引擎
    - [ ] 设计担保审批流程BPMN文件
    - [ ] 实现流程启动和任务分配
    - [ ] 实现流程跳转和完成
  - [ ] 实现审批超时提醒功能
  - [ ] 编写审批相关单元测试
  - [ ] 前端待审批任务列表页面 `src/views/danbao/approval/task.vue`
  - [ ] 前端审批处理页面 `src/views/danbao/approval/handle.vue`
  - [ ] 前端审批历史页面 `src/views/danbao/approval/history.vue`
  - [ ] 前端审批流程配置页面 `src/views/danbao/approval/flow-config.vue`

- [ ] **任务2.3：反担保物管理功能开发**
  - [ ] **数据库开发**
    - [ ] 创建 `danbao_collateral_category` 表SQL脚本
    - [ ] 创建 `danbao_collateral` 表SQL脚本
    - [ ] 创建 `danbao_collateral_detail` 表SQL脚本
    - [ ] 创建 `danbao_collateral_storage` 表SQL脚本
    - [ ] 生成押品类别基础数据SQL脚本
    - [ ] 生成完整SQL脚本到 `docs/sql/collateral_init.sql`
    - [ ] 连接数据库执行SQL脚本
  - [ ] **后端开发**
    - [ ] 创建反担保物相关DO实体类
    - [ ] 创建反担保物相关Mapper接口和XML
    - [ ] 创建反担保物相关VO类和Convert转换器
    - [ ] 创建 `CollateralService` 接口和实现类
    - [ ] 创建 `CollateralController` 控制器
  - [ ] **前端开发**
    - [ ] 创建押品管理页面 `src/views/danbao/collateral/index.vue`
    - [ ] 创建押品详情页面
    - [ ] 创建押品入库出库页面
  - [ ] **菜单权限配置**
    - [ ] 创建"反担保物管理"主菜单和相关权限
  - [ ] **单元测试**
    - [ ] 编写反担保物管理相关单元测试

- [ ] **任务2.4：档案管理功能开发**
  - [ ] **数据库开发**
    - [ ] 创建 `danbao_archive` 表SQL脚本
    - [ ] 创建 `danbao_archive_file` 表SQL脚本
    - [ ] 创建 `danbao_archive_borrow` 表SQL脚本
    - [ ] 生成字典项SQL脚本（档案类型、借阅状态等）
    - [ ] 生成完整SQL脚本到 `docs/sql/archive_init.sql`
    - [ ] 连接数据库执行SQL脚本
  - [ ] **后端开发**
    - [ ] 创建档案管理相关DO实体类
    - [ ] 创建档案管理相关Mapper接口和XML
    - [ ] 创建档案管理相关VO类和Convert转换器
    - [ ] 创建 `ArchiveService` 接口和实现类
    - [ ] 创建 `ArchiveController` 控制器
  - [ ] **前端开发**
    - [ ] 创建档案管理页面 `src/views/danbao/archive/index.vue`
    - [ ] 创建档案借阅页面
    - [ ] 创建档案归还页面
  - [ ] **菜单权限配置**
    - [ ] 创建"档案管理"主菜单和相关权限
  - [ ] **单元测试**
    - [ ] 编写档案管理相关单元测试

- [ ] **任务2.5：综合管理功能开发**
  - [ ] **数据库开发**
    - [ ] 创建 `danbao_partner_institution` 表SQL脚本
    - [ ] 创建 `danbao_institution_account` 表SQL脚本
    - [ ] 生成字典项SQL脚本（机构类型、机构分类等）
    - [ ] 生成完整SQL脚本到 `docs/sql/institution_init.sql`
    - [ ] 连接数据库执行SQL脚本
  - [ ] **后端开发**
    - [ ] 创建综合管理相关DO实体类
    - [ ] 创建综合管理相关Mapper接口和XML
    - [ ] 创建综合管理相关VO类和Convert转换器
    - [ ] 创建 `InstitutionService` 接口和实现类
    - [ ] 创建 `InstitutionController` 控制器
  - [ ] **前端开发**
    - [ ] 创建合作机构管理页面 `src/views/danbao/institution/index.vue`
  - [ ] **菜单权限配置**
    - [ ] 创建"综合管理"主菜单和相关权限
  - [ ] **单元测试**
    - [ ] 编写综合管理相关单元测试

- [ ] **任务2.6：客户管理功能开发**
  - [ ] 创建 `CustomerService` 接口
  - [ ] 实现 `CustomerServiceImpl` 业务逻辑
  - [ ] 创建 `CustomerController` 控制器
  - [ ] 实现客户编号自动生成
  - [ ] 实现客户资信评级功能
  - [ ] 实现客户风险等级评估
  - [ ] 实现客户财务信息管理
  - [ ] 编写客户相关单元测试
  - [ ] 前端客户管理页面开发

#### 验收标准
- 所有Service接口方法实现完整，业务逻辑正确
- 所有Controller接口响应正确，参数校验完整
- 工作流引擎集成成功，审批流程运行正常
- 前端页面功能完整，用户体验良好
- 单元测试和集成测试通过
- 接口文档完整，符合OpenAPI规范

### 第三阶段：风险管控功能开发 (第9-12周)

#### 任务清单
- [ ] **任务3.1：风险评估功能开发**
  - [ ] 创建风险评估相关数据表
    - [ ] 创建 `danbao_risk_assessment` 表SQL脚本
    - [ ] 创建 `danbao_risk_rule` 表SQL脚本
    - [ ] 创建 `danbao_risk_indicator` 表SQL脚本
  - [ ] 创建风险评估相关实体类和Mapper
  - [ ] 创建 `RiskAssessmentService` 接口
  - [ ] 实现风险评估算法
    - [ ] 实现财务风险评估算法
    - [ ] 实现信用风险评估算法
    - [ ] 实现行业风险评估算法
    - [ ] 实现区域风险评估算法
    - [ ] 实现综合风险评估算法
  - [ ] 创建 `RiskAssessmentController` 控制器
  - [ ] 实现风险评估报告生成
  - [ ] 前端风险评估页面开发
  - [ ] 编写风险评估单元测试

- [ ] **任务3.2：风险监控功能开发**
  - [ ] 创建风险监控相关数据表
    - [ ] 创建 `danbao_risk_monitor` 表SQL脚本
    - [ ] 创建 `danbao_risk_warning` 表SQL脚本
    - [ ] 创建 `danbao_risk_event` 表SQL脚本
  - [ ] 创建风险监控相关实体类和Mapper
  - [ ] 创建 `RiskMonitorService` 接口
  - [ ] 实现风险监控功能
    - [ ] 实现风险指标监控
    - [ ] 实现风险预警规则配置
    - [ ] 实现风险预警触发机制
    - [ ] 实现风险事件记录和处理
  - [ ] 集成消息通知功能
    - [ ] 实现邮件预警通知
    - [ ] 实现短信预警通知
    - [ ] 实现站内消息通知
  - [ ] 创建 `RiskMonitorController` 控制器
  - [ ] 前端风险监控大屏开发
  - [ ] 前端风险预警管理页面开发
  - [ ] 编写风险监控单元测试

- [ ] **任务3.3：代偿管理功能开发**
  - [ ] 创建代偿管理相关数据表
    - [ ] 创建 `danbao_compensation` 表SQL脚本
    - [ ] 创建 `danbao_recovery_record` 表SQL脚本
    - [ ] 创建 `danbao_write_off_record` 表SQL脚本
  - [ ] 创建代偿管理相关实体类和Mapper
  - [ ] 创建 `CompensationService` 接口
  - [ ] 实现代偿管理功能
    - [ ] 实现代偿申请处理
    - [ ] 实现代偿资金支付
    - [ ] 实现追偿计划制定
    - [ ] 实现追偿执行跟踪
    - [ ] 实现损失核销处理
  - [ ] 创建 `CompensationController` 控制器
  - [ ] 前端代偿管理页面开发
  - [ ] 编写代偿管理单元测试

#### 验收标准
- 风险评估算法准确，评估结果合理
- 风险监控实时有效，预警及时准确
- 代偿管理流程完整，数据记录准确
- 消息通知功能正常，通知及时到达
- 前端页面功能完整，数据展示清晰
- 单元测试覆盖率达到80%以上

### 第四阶段：财务管理开发 (第13-15周)

#### 任务清单
- [ ] **任务4.1：费用管理功能开发**
  - [ ] 创建费用管理相关数据表
    - [ ] 创建 `danbao_fee_config` 表SQL脚本
    - [ ] 创建 `danbao_fee_record` 表SQL脚本
    - [ ] 创建 `danbao_fee_invoice` 表SQL脚本
  - [ ] 创建费用管理相关实体类和Mapper
  - [ ] 创建 `FeeService` 接口
  - [ ] 实现费用管理功能
    - [ ] 实现费用配置管理
    - [ ] 实现费用计算算法
    - [ ] 实现费用收取记录
    - [ ] 实现发票生成功能
    - [ ] 实现费用统计分析
  - [ ] 创建 `FeeController` 控制器
  - [ ] 前端费用管理页面开发
  - [ ] 编写费用管理单元测试

- [ ] **任务4.2：统计分析功能开发**
  - [ ] 创建统计分析相关数据表
    - [ ] 创建 `danbao_statistics_config` 表SQL脚本
    - [ ] 创建 `danbao_statistics_cache` 表SQL脚本
  - [ ] 创建 `StatisticsService` 接口
  - [ ] 实现统计分析功能
    - [ ] 实现业务量统计
    - [ ] 实现客户分布统计
    - [ ] 实现产品使用统计
    - [ ] 实现风险分布统计
    - [ ] 实现收入利润统计
    - [ ] 实现趋势分析统计
  - [ ] 创建 `StatisticsController` 控制器
  - [ ] 实现数据缓存机制
  - [ ] 前端统计分析大屏开发
  - [ ] 前端各类统计报表页面开发
  - [ ] 编写统计分析单元测试

- [ ] **任务4.3：报表管理功能开发**
  - [ ] 创建报表管理相关数据表
    - [ ] 创建 `danbao_report_template` 表SQL脚本
    - [ ] 创建 `danbao_report_task` 表SQL脚本
    - [ ] 创建 `danbao_report_history` 表SQL脚本
  - [ ] 创建报表管理相关实体类和Mapper
  - [ ] 创建 `ReportService` 接口
  - [ ] 实现报表管理功能
    - [ ] 实现报表模板管理
    - [ ] 实现报表生成引擎
    - [ ] 实现Excel报表导出
    - [ ] 实现PDF报表导出
    - [ ] 实现定时报表任务
    - [ ] 实现报表权限控制
  - [ ] 创建 `ReportController` 控制器
  - [ ] 前端报表管理页面开发
  - [ ] 编写报表管理单元测试

#### 验收标准
- 费用计算准确，收费记录完整
- 统计数据准确，分析结果合理
- 报表生成正常，导出格式正确
- 定时任务运行稳定，权限控制有效
- 前端图表展示美观，交互体验良好
- 单元测试覆盖率达到80%以上

### 第七阶段：系统集成测试和优化 (第19-20周)

#### 任务清单
- [ ] **任务5.1：功能测试完善**
  - [ ] 完善单元测试
    - [ ] 补充Service层单元测试
    - [ ] 补充Controller层单元测试
    - [ ] 补充Mapper层单元测试
    - [ ] 确保测试覆盖率达到80%以上
  - [ ] 执行集成测试
    - [ ] 测试担保申请到审批完整流程
    - [ ] 测试客户管理完整功能
    - [ ] 测试风险评估和监控功能
    - [ ] 测试费用管理和统计功能
    - [ ] 测试报表生成和导出功能
  - [ ] 执行系统测试
    - [ ] 测试系统稳定性
    - [ ] 测试数据一致性
    - [ ] 测试异常处理
    - [ ] 测试边界条件
  - [ ] 用户验收测试
    - [ ] 准备测试数据和测试用例
    - [ ] 组织用户进行功能验收
    - [ ] 收集用户反馈和改进建议
    - [ ] 修复验收测试发现的问题

- [ ] **任务5.2：性能测试和优化**
  - [ ] 执行性能测试
    - [ ] 数据库查询性能测试
    - [ ] 接口响应时间测试
    - [ ] 并发用户访问测试
    - [ ] 大数据量处理测试
  - [ ] 性能优化
    - [ ] 数据库索引优化
    - [ ] SQL查询优化
    - [ ] 缓存机制优化
    - [ ] 接口性能优化
  - [ ] 负载测试
    - [ ] 模拟高并发访问
    - [ ] 测试系统承载能力
    - [ ] 识别性能瓶颈
    - [ ] 制定扩容方案

- [ ] **任务5.3：安全测试和加固**
  - [ ] 安全漏洞扫描
    - [ ] SQL注入漏洞扫描
    - [ ] XSS攻击漏洞扫描
    - [ ] CSRF攻击漏洞扫描
    - [ ] 文件上传漏洞扫描
  - [ ] 权限测试
    - [ ] 测试用户权限控制
    - [ ] 测试数据权限控制
    - [ ] 测试接口权限控制
    - [ ] 测试菜单权限控制
  - [ ] 数据安全测试
    - [ ] 测试敏感数据加密
    - [ ] 测试数据传输安全
    - [ ] 测试数据备份恢复
    - [ ] 测试审计日志记录
  - [ ] 安全加固
    - [ ] 修复发现的安全漏洞
    - [ ] 加强输入参数校验
    - [ ] 完善异常处理机制
    - [ ] 优化日志记录策略

#### 验收标准
- 所有功能测试用例通过
- 系统性能满足预期要求
- 安全测试无高危漏洞
- 用户验收测试通过
- 测试文档完整规范

### 第八阶段：部署上线和交付 (第21-24周)

#### 任务清单
- [ ] **任务6.1：生产环境部署**
  - [ ] 生产环境准备
    - [ ] 服务器环境配置
    - [ ] 数据库环境配置
    - [ ] 网络安全配置
    - [ ] 负载均衡配置
  - [ ] 应用系统部署
    - [ ] 后端应用部署
    - [ ] 前端应用部署
    - [ ] 静态资源部署
    - [ ] 配置文件调整
  - [ ] 系统监控配置
    - [ ] 应用监控配置
    - [ ] 数据库监控配置
    - [ ] 服务器监控配置
    - [ ] 日志监控配置
  - [ ] 部署验证
    - [ ] 功能验证测试
    - [ ] 性能验证测试
    - [ ] 安全验证测试
    - [ ] 监控验证测试

- [ ] **任务6.2：数据迁移和初始化**
  - [ ] 历史数据整理
    - [ ] 分析现有数据结构
    - [ ] 制定数据迁移方案
    - [ ] 清理和标准化数据
    - [ ] 数据质量检查
  - [ ] 数据迁移执行
    - [ ] 编写数据迁移脚本
    - [ ] 执行数据迁移
    - [ ] 数据完整性验证
    - [ ] 数据一致性检查
  - [ ] 系统初始化
    - [ ] 基础数据初始化
    - [ ] 用户权限初始化
    - [ ] 系统参数配置
    - [ ] 业务规则配置
  - [ ] 数据备份
    - [ ] 制定备份策略
    - [ ] 执行数据备份
    - [ ] 验证备份完整性
    - [ ] 测试数据恢复

- [ ] **任务6.3：用户培训和交付**
  - [ ] 培训材料准备
    - [ ] 编写用户操作手册
    - [ ] 制作培训PPT
    - [ ] 录制操作视频
    - [ ] 准备培训环境
  - [ ] 用户培训实施
    - [ ] 管理员培训
    - [ ] 业务用户培训
    - [ ] 系统维护培训
    - [ ] 培训效果评估
  - [ ] 系统交付
    - [ ] 编写系统交付文档
    - [ ] 整理技术文档
    - [ ] 移交源代码
    - [ ] 签署验收报告
  - [ ] 上线支持
    - [ ] 7x24小时上线支持
    - [ ] 问题快速响应
    - [ ] 系统稳定性监控
    - [ ] 用户反馈收集

#### 验收标准
- 生产环境部署成功，系统运行稳定
- 数据迁移完整，数据质量良好
- 用户培训效果良好，操作熟练
- 系统交付文档完整，验收通过
- 上线支持及时，问题处理迅速

## 开发规范和技术要求

### 代码开发规范 (严格按照"管理后台 - 担保申请"模块标准)

1. **命名规范**
   - 实体类：`XxxDO` (如：ApplicationDO、CustomerDO)
   - VO类：`XxxPageReqVO`、`XxxSaveReqVO`、`XxxRespVO`
   - Service接口：`XxxService` (如：ApplicationService、CustomerService)
   - Service实现：`XxxServiceImpl`
   - Controller：`XxxController` (如：ApplicationController、CustomerController)
   - Mapper接口：`XxxMapper` (如：ApplicationMapper、CustomerMapper)
   - Convert转换器：`XxxConvert`

2. **DO实体类规范** (参考AdminUserDO)
   - **租户隔离**：必须继承 `TenantBaseDO`（自动包含租户隔离功能）
   - `TenantBaseDO` 已继承 `TenantBaseDO`（租户隔离），包含所有通用字段：creator、create_time、updater、update_time、deleted、tenantId
   - 使用 `@TableName("表名")` 指定数据库表名
   - 使用 `@KeySequence("表名_seq")` 支持多数据库主键自增
   - 使用标准注解：`@Data`、`@EqualsAndHashCode(callSuper = true)`、`@Builder`、`@NoArgsConstructor`、`@AllArgsConstructor`
   - 可包含业务方法（如数据处理、状态转换等）
   - **重要**：租户隔离由框架自动处理，无需手动处理tenantId字段

3. **VO类规范**
   - `PageReqVO` 必须继承 `PageParam`
   - `SaveReqVO` 包含id字段，用于新增和修改
   - `RespVO` 包含 `@ExcelProperty` 注解支持导出，使用 `@ExcelIgnoreUnannotated`
   - 所有VO类使用 `@Schema` 注解描述字段
   - 时间范围查询使用 `LocalDateTime[]` 类型
   - 使用校验注解：`@NotNull`、`@NotBlank`、`@Valid` 等

4. **Mapper接口规范**
   - 必须继承 `BaseMapperX<DO>`
   - 使用 `@Mapper` 注解
   - 分页查询方法：`default PageResult<DO> selectPage(PageReqVO reqVO)`
   - 使用 `LambdaQueryWrapperX` 构建查询条件
   - 查询条件使用 `eqIfPresent`、`likeIfPresent`、`betweenIfPresent` 等方法
   - 排序使用 `orderByDesc(DO::getId)` 等方法

5. **Service规范**
   - 接口方法命名：`createXxx`、`updateXxx`、`deleteXxx`、`getXxx`、`getXxxPage`
   - 参数使用 `@Valid` 校验
   - 返回类型：`Long`（创建）、`void`（更新删除）、`DO`（单个）、`PageResult<DO>`（分页）
   - 实现类使用 `@Service` 和 `@Validated` 注解
   - 使用 `BeanUtils.toBean` 进行对象转换
   - 包含数据存在性校验方法：`validateXxxExists(Long id)`

6. **Controller规范**
   - 使用 `@Tag(name = "管理后台 - 模块名")` 描述
   - 请求映射：`@RequestMapping("/danbao/模块名")`
   - 标准接口路径：`/create`、`/update`、`/delete`、`/get`、`/page`、`/export`
   - 使用 `@PreAuthorize("@ss.hasPermission('danbao:模块:权限')")` 权限控制
   - 使用 `@Operation(summary = "接口描述")` 描述接口
   - 参数使用 `@Parameter` 或 `@RequestBody` 注解
   - 返回 `CommonResult<T>` 格式
   - 使用 `BeanUtils.toBean` 转换响应对象

7. **Convert转换器规范**
   - 使用 `@Mapper` 注解
   - 定义 `INSTANCE = Mappers.getMapper(XxxConvert.class)` 常量
   - 实现各种VO和DO之间的转换方法
   - 支持List转换：`List<RespVO> convertList(List<DO> list)`
   - 可使用 `@Mapping` 注解处理特殊字段转换

2. **包结构规范**
   ```
   com.nodal.module.danbao
   ├── controller.admin     # 管理端控制器
   ├── controller.app       # 应用端控制器
   ├── service             # 业务服务层
   ├── dal.dataobject      # 数据对象
   ├── dal.mysql           # MySQL映射器
   ├── dal.redis           # Redis操作
   ├── convert             # 对象转换器
   ├── framework           # 框架配置
   └── enums               # 枚举类
   ```

8. **数据库设计规范** (严格按照系统标准)
   - 表名：`danbao_` + 业务名称（如：danbao_application）
   - 字段名：使用下划线命名法
   - 主键：统一使用 `id` (BIGINT AUTO_INCREMENT)
   - 特殊字段：`create_date` (BIGINT，yyyyMMdd格式，自动填充)
   - 必须包含通用字段：
     - `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者'
     - `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
     - `updater` VARCHAR(64) DEFAULT '' COMMENT '更新者'
     - `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
     - `deleted` BIT(1) NOT NULL DEFAULT 0 COMMENT '是否删除'
     - `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号'
   - 数据类型规范：
     - 金额字段：DECIMAL(15,2)
     - 状态字段：TINYINT，从0开始
     - 字符串字段：VARCHAR，根据业务需要设置长度
     - 日期时间：DATETIME
     - 布尔字段：BIT(1)
   - 索引规范：
     - 主键自动创建聚集索引
     - 外键字段创建普通索引：INDEX idx_字段名 (字段名)
     - 查询频繁的字段创建索引
     - 状态字段创建索引
   - 字符集：utf8mb4
   - 排序规则：utf8mb4_unicode_ci
   - 存储引擎：InnoDB

9. **接口设计规范** (严格按照系统标准)
   - 管理端接口基础路径：`/admin-api/danbao/模块名`
   - 应用端接口基础路径：`/app-api/danbao/模块名`
   - 标准CRUD接口路径：
     - 创建：`POST /create`
     - 更新：`PUT /update`
     - 删除：`DELETE /delete?id={id}`
     - 详情：`GET /get?id={id}`
     - 分页：`GET /page`
     - 导出：`GET /export-excel`
   - 统一返回格式：`CommonResult<T>`
   - 分页查询：使用 `PageResult<T>`
   - 参数校验：使用 `@Valid` 和校验注解
   - 权限控制：`@PreAuthorize("@ss.hasPermission('danbao:模块:权限')")`
   - 权限标识规范：
     - 查询：`danbao:模块:query`
     - 创建：`danbao:模块:create`
     - 更新：`danbao:模块:update`
     - 删除：`danbao:模块:delete`
     - 导出：`danbao:模块:export`

10. **前端开发规范** (参考用户模块结构)
    - API接口文件：`src/api/danbao/模块名.ts`
    - 页面文件：`src/views/danbao/模块名/index.vue`（列表页）
    - 表单页面：`src/views/danbao/模块名/form.vue`
    - 详情页面：`src/views/danbao/模块名/detail.vue`
    - 组件命名：使用PascalCase
    - 路由配置：在对应的路由文件中配置

### 技术实现要点
1. **工作流引擎集成**
   - 使用Flowable 7.0.1版本
   - 审批流程BPMN文件放在 `resources/processes/` 目录
   - 流程变量命名：`applicationId`、`approvalResult`等
   - 任务监听器：实现审批结果处理逻辑

2. **风险评估算法**
   - 财务评分：基于资产负债率、净利润率等指标
   - 信用评分：基于征信记录、历史违约情况
   - 行业评分：基于行业风险系数配置
   - 综合评分：加权平均计算，权重可配置

3. **文件管理**
   - 支持格式：PDF、DOC、DOCX、JPG、PNG
   - 文件大小限制：单个文件不超过10MB
   - 存储路径：`/danbao/application/{applicationId}/`
   - 文件预览：集成在线预览组件

4. **消息通知**
   - 邮件通知：使用Spring Mail
   - 短信通知：集成阿里云短信服务
   - 站内消息：使用WebSocket推送
   - 通知模板：支持变量替换

5. **数据安全**
   - 敏感字段加密：身份证号、银行账号等
   - 接口权限控制：基于Spring Security
   - 数据权限控制：基于租户和部门
   - 操作日志记录：记录关键业务操作

6. **性能优化**
   - 数据库索引：为查询字段建立合适索引
   - 查询优化：避免N+1查询，使用批量查询
   - 缓存策略：对字典数据、配置数据使用Redis缓存
   - 分页查询：大数据量查询必须分页

## 开发任务优先级

### 高优先级任务（必须完成）
1. **担保申请管理** - 核心业务流程
2. **担保审批管理** - 核心业务流程
3. **客户管理** - 基础数据管理
4. **基础数据字典** - 系统基础功能

### 中优先级任务（重要功能）
1. **保后管理** - ✅ 已完成（保后检查、还款登记、担保结算、代偿追偿、风险预警）
2. **风险评估** - 业务风控需求
3. **费用管理** - 财务管理需求
4. **统计分析** - 业务分析需求
5. **报表管理** - 数据展示需求

### 低优先级任务（增强功能）
1. **风险监控** - 高级风控功能
2. **代偿管理** - 特殊业务场景
3. **消息通知** - 用户体验优化
4. **系统监控** - 运维管理功能

## 质量保证要求

### 代码质量要求
- 代码覆盖率：单元测试覆盖率不低于80%
- 代码规范：严格遵循阿里巴巴Java开发手册
- 代码审查：所有代码必须经过Code Review
- 文档完整：接口文档、数据库文档、部署文档齐全

### 性能要求
- 接口响应时间：普通查询接口响应时间不超过500ms
- 并发处理：支持100个并发用户同时操作
- 数据处理：支持单表100万条数据的查询和统计
- 系统可用性：系统可用性不低于99.5%

### 安全要求
- 数据加密：敏感数据必须加密存储
- 权限控制：严格的用户权限和数据权限控制
- 安全审计：完整的操作日志和审计跟踪
- 漏洞扫描：定期进行安全漏洞扫描和修复

## 项目交付标准

### 代码交付
- [ ] 完整的源代码，包含前端和后端
- [ ] 数据库设计文档和建表脚本
- [ ] 接口文档（Swagger/OpenAPI格式）
- [ ] 部署文档和配置说明
- [ ] 单元测试和集成测试代码

### 文档交付
- [ ] 系统设计文档
- [ ] 数据库设计文档
- [ ] 接口设计文档
- [ ] 用户操作手册
- [ ] 系统管理员手册
- [ ] 部署运维文档

### 培训交付
- [ ] 用户培训材料
- [ ] 管理员培训材料
- [ ] 开发人员技术文档
- [ ] 系统维护指南

## 租户隔离实现说明

### 🔒 **租户隔离核心要点**：

1. **实体类租户隔离**
   - 所有业务实体类必须继承 `TenantBaseDO`
   - `TenantBaseDO` 自动包含 `tenantId` 字段
   - 框架自动处理租户数据隔离，无需手动处理

2. **数据访问租户隔离**
   - Mapper查询自动添加租户条件
   - 插入数据自动设置当前租户ID
   - 更新删除操作自动限制在当前租户范围

3. **Service层租户隔离**
   - 参考 `AdminUserServiceImpl` 的实现方式
   - 使用 `tenantService.handleTenantInfo()` 处理租户相关逻辑
   - 租户配额检查、权限验证等

4. **Controller层租户隔离**
   - 框架自动处理，无需额外代码
   - 用户只能访问自己租户的数据

### 📋 **与用户模块对比**：

| 对比项 | 地址库模块 | 用户模块 | 担保系统采用 |
|--------|------------|----------|--------------|
| 基类继承 | `BaseDO` | `TenantBaseDO` | ✅ `TenantBaseDO` |
| 租户隔离 | ❌ 无 | ✅ 自动隔离 | ✅ 自动隔离 |
| 数据安全 | 普通 | 高安全 | ✅ 高安全 |
| 多租户支持 | ❌ 不支持 | ✅ 完整支持 | ✅ 完整支持 |

## 需求覆盖度分析

### ✅ **已完整覆盖的需求功能**：

1. **工作台** - 通知消息、待办事项、已办事项、预警信息、业绩统计
2. **客户管理** - 客户信息采集、个人/企业客户信息维护、财务报表、客户评级、影像资料管理
3. **反担保物管理** - 押品类别管理、押品信息管理、押品入库出库、押品查询
4. **业务办理** - 项目立项、项目尽调、担保审批、合同管理、收费管理、放款管理
5. **费用管理** - 费用设置、费用收取、保证金管理
6. **保后管理** - 保后跟踪、还款登记、担保结算、代偿追偿、风险预警、业务变更
7. **综合管理** - 资金机构管理、合作机构管理
8. **档案管理** - 档案归档、档案变更、借阅申请、出借审批、档案归还
9. **财务管理** - 财务设置、凭证管理
10. **统计报表** - 担保业务监控、业务统计、监控预警
11. **系统设置** - 机构设置、客户设置、产品设置、保后设置、基础设置
12. **移动端管理** - 客户管理、资料上传、业务查询、线上审批、保后检查、业务预警

### 🔧 **技术需求覆盖**：

1. **微服务架构** - 基于Spring Boot 3.x的微服务架构
2. **分布式部署** - 支持高可用性和平行扩展
3. **权限管理** - 完善的RBAC权限控制体系
4. **工作流引擎** - 集成Flowable工作流引擎
5. **多数据库支持** - MySQL、Oracle、PostgreSQL等
6. **安全性** - 数据加密、权限控制、安全审计
7. **性能要求** - 响应时间、并发处理、缓存策略
8. **移动端集成** - 钉钉、企微集成支持

### 📊 **开发计划完整性**：

1. **8个开发阶段**：从数据库设计到系统上线的完整流程
2. **详细任务清单**：每个任务都有明确的开发要求和验收标准
3. **技术规范**：统一的代码规范和技术实现标准
4. **质量保证**：完整的测试和质量控制要求
5. **交付标准**：明确的项目交付物和验收标准

### 🎯 **关键业务流程覆盖**：

- **担保业务全流程**：受理 → 尽调 → 评审 → 签约 → 保后 → 代偿 → 解保
- **风险管控全流程**：风险评估 → 风险监控 → 预警处理 → 风险处置
- **客户管理全流程**：客户采集 → 信息维护 → 评级管理 → 关系维护
- **档案管理全流程**：档案归档 → 借阅管理 → 归还管理

## 总结

本开发计划已完全覆盖需求规格说明书中的所有功能要求，包含：

1. **功能完整性**：涵盖担保业务的所有核心功能和辅助功能
2. **技术先进性**：采用现代化的微服务架构和技术栈
3. **业务适配性**：严格按照担保行业的业务流程设计
4. **扩展性**：支持未来业务发展和功能扩展需求
5. **合规性**：满足监管要求和内控合规需求

通过按照此计划执行开发任务，可以确保担保系统的高质量交付，完全满足河南农投信用服务有限公司的业务需求和技术要求。
   - 主键：统一使用 `id` (BIGINT)
   - 必须包含通用字段：creator、create_time、updater、update_time、deleted、tenant_id
   - 金额字段：使用 DECIMAL(15,2)
   - 状态字段：使用 TINYINT，从0开始

4. **接口设计规范**
   - 管理端接口：`/admin-api/danbao/模块名/方法名`
   - 应用端接口：`/app-api/danbao/模块名/方法名`
   - 统一返回格式：`CommonResult<T>`
   - 分页查询：使用 `PageResult<T>`
   - 参数校验：使用 `@Valid` 和校验注解

### 技术实现要点
1. **工作流引擎集成**
   - 使用Flowable 7.0.1版本
   - 审批流程BPMN文件放在 `resources/processes/` 目录
   - 流程变量命名：`applicationId`、`approvalResult`等
   - 任务监听器：实现审批结果处理逻辑

2. **风险评估算法**
   - 财务评分：基于资产负债率、净利润率等指标
   - 信用评分：基于征信记录、历史违约情况
   - 行业评分：基于行业风险系数配置
   - 综合评分：加权平均计算，权重可配置

3. **文件管理**
   - 支持格式：PDF、DOC、DOCX、JPG、PNG
   - 文件大小限制：单个文件不超过10MB
   - 存储路径：`/danbao/application/{applicationId}/`
   - 文件预览：集成在线预览组件

4. **消息通知**
   - 邮件通知：使用Spring Mail
   - 短信通知：集成阿里云短信服务
   - 站内消息：使用WebSocket推送
   - 通知模板：支持变量替换

5. **数据安全**
   - 敏感字段加密：身份证号、银行账号等
   - 接口权限控制：基于Spring Security
   - 数据权限控制：基于租户和部门
   - 操作日志记录：记录关键业务操作

6. **性能优化**
   - 数据库索引：为查询字段建立合适索引
   - 查询优化：避免N+1查询，使用批量查询
   - 缓存策略：对字典数据、配置数据使用Redis缓存
   - 分页查询：大数据量查询必须分页

## 项目里程碑

| 里程碑 | 时间节点 | 主要交付物 |
|--------|----------|------------|
| M1 | 第2周末 | 基础框架和数据库设计完成 |
| M2 | 第6周末 | 核心业务模块开发完成 |
| M3 | 第9周末 | 风险管控模块开发完成 |
| M4 | 第12周末 | 财务统计模块开发完成 |
| M5 | 第14周末 | 系统集成测试完成 |
| M6 | 第16周末 | 系统部署上线完成 |

## 风险控制

### 技术风险
- **风险**: 复杂业务逻辑实现难度大
- **应对**: 分阶段开发，及时技术评审

### 进度风险
- **风险**: 需求变更导致进度延期
- **应对**: 需求冻结机制，变更控制流程

### 质量风险
- **风险**: 测试不充分导致质量问题
- **应对**: 完善测试体系，自动化测试

## 资源配置

### 开发团队
- 项目经理: 1人
- 后端开发: 2-3人
- 前端开发: 2人
- 测试工程师: 1人
- 运维工程师: 1人

### 开发工具
- IDE: IntelliJ IDEA / VS Code
- 版本控制: Git
- 项目管理: JIRA / 禅道
- 文档管理: Confluence / 语雀

## 总结

本开发计划基于担保行业的业务特点和技术要求制定，采用敏捷开发模式，分阶段交付，确保项目按时保质完成。在开发过程中需要密切关注业务需求变化，及时调整开发计划，确保最终交付的系统能够满足担保业务的实际需要。
